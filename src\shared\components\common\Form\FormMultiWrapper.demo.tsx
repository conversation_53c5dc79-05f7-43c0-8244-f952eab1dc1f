import React from 'react';
import { FormMultiWrapper, Form, FormItem } from './';
import { Input, <PERSON>ton, Card } from '../';
import { z } from 'zod';
import { FieldValues, SubmitHandler } from 'react-hook-form';

// Schema cho demo form
const demoSchema = z.object({
  name: z.string().min(2, 'Tên phải có ít nhất 2 ký tự'),
  email: z.string().email('Email không hợp lệ'),
  phone: z.string().min(10, 'Số điện thoại phải có ít nhất 10 số'),
});

type DemoFormValues = z.infer<typeof demoSchema>;

/**
 * Demo component cho FormMultiWrapper
 */
const FormMultiWrapperDemo: React.FC = () => {
  const handleSubmit: SubmitHandler<FieldValues> = (data) => {
    console.log('Form submitted:', data);
    alert('Form đã được gửi thành công!');
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">FormMultiWrapper Demo</h1>
        <p className="text-muted-foreground">
          Component bọc form với div container và Typography title
        </p>
      </div>

      {/* Demo 1: Basic Form Wrapper */}
      <Card title="1. Form Wrapper Cơ Bản" className="p-6">
        <FormMultiWrapper title="Thông tin cá nhân">
          <Form schema={demoSchema} onSubmit={handleSubmit}>
            <FormItem name="name" label="Họ tên" required>
              <Input placeholder="Nhập họ tên" />
            </FormItem>
            
            <FormItem name="email" label="Email" required>
              <Input type="email" placeholder="Nhập email" />
            </FormItem>
            
            <FormItem name="phone" label="Số điện thoại" required>
              <Input placeholder="Nhập số điện thoại" />
            </FormItem>
            
            <div className="flex gap-2 pt-4">
              <Button type="submit" variant="primary">
                Lưu thông tin
              </Button>
              <Button type="button" variant="outline">
                Hủy
              </Button>
            </div>
          </Form>
        </FormMultiWrapper>
      </Card>

      {/* Demo 2: Form với Title Border */}
      <Card title="2. Form với Title Border" className="p-6">
        <FormMultiWrapper 
          title="Đăng ký tài khoản" 
          titleBorder={true}
          titleVariant="h4"
          titleColor="primary"
        >
          <Form schema={demoSchema} onSubmit={handleSubmit}>
            <FormItem name="name" label="Họ tên" required>
              <Input placeholder="Nhập họ tên" />
            </FormItem>
            
            <FormItem name="email" label="Email" required>
              <Input type="email" placeholder="Nhập email" />
            </FormItem>
            
            <Button type="submit" variant="primary" className="w-full">
              Đăng ký
            </Button>
          </Form>
        </FormMultiWrapper>
      </Card>

      {/* Demo 3: Form với Background và Padding */}
      <Card title="3. Form với Background và Padding" className="p-6">
        <FormMultiWrapper 
          title="Liên hệ với chúng tôi"
          titleVariant="h4"
          titleAlign="center"
          background="card"
          padding="lg"
          radius="lg"
          shadow="md"
          spacing="md"
        >
          <Form schema={demoSchema} onSubmit={handleSubmit}>
            <FormItem name="name" label="Họ tên" required>
              <Input placeholder="Nhập họ tên" />
            </FormItem>
            
            <FormItem name="email" label="Email" required>
              <Input type="email" placeholder="Nhập email" />
            </FormItem>
            
            <FormItem name="phone" label="Số điện thoại" required>
              <Input placeholder="Nhập số điện thoại" />
            </FormItem>
            
            <Button type="submit" variant="primary" className="w-full">
              Gửi liên hệ
            </Button>
          </Form>
        </FormMultiWrapper>
      </Card>

      {/* Demo 4: Form Compact (No Spacing) */}
      <Card title="4. Form Compact - Không có khoảng cách" className="p-6">
        <FormMultiWrapper 
          title="Form compact"
          titleVariant="h6"
          spacing="none"
        >
          <Form schema={demoSchema} onSubmit={handleSubmit}>
            <FormItem name="name" label="Họ tên" required>
              <Input placeholder="Nhập họ tên" />
            </FormItem>
            
            <FormItem name="email" label="Email" required>
              <Input type="email" placeholder="Nhập email" />
            </FormItem>
            
            <Button type="submit" variant="primary" size="sm">
              Lưu
            </Button>
          </Form>
        </FormMultiWrapper>
      </Card>

      {/* Demo 5: Các Title Variants */}
      <Card title="5. Các Title Variants" className="p-6">
        <div className="space-y-6">
          {(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'subtitle1', 'subtitle2'] as const).map((variant) => (
            <FormMultiWrapper 
              key={variant}
              title={`Title với variant "${variant}"`}
              titleVariant={variant}
              className="border border-border rounded p-4"
            >
              <p className="text-sm text-muted-foreground">
                Đây là nội dung form với title variant {variant}
              </p>
            </FormMultiWrapper>
          ))}
        </div>
      </Card>

      {/* Demo 6: Các Title Colors */}
      <Card title="6. Các Title Colors" className="p-6">
        <div className="space-y-4">
          {(['default', 'primary', 'secondary', 'success', 'warning', 'error', 'muted'] as const).map((color) => (
            <FormMultiWrapper 
              key={color}
              title={`Title với màu "${color}"`}
              titleColor={color}
              titleVariant="h5"
              className="border border-border rounded p-4"
            >
              <p className="text-sm text-muted-foreground">
                Đây là nội dung form với title color {color}
              </p>
            </FormMultiWrapper>
          ))}
        </div>
      </Card>
    </div>
  );
};

export default FormMultiWrapperDemo;
